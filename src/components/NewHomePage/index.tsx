"use client";
import React from "react";
import WhyPeopleChoose from "@/components/NewHomePage/components/WhyPeopleChoose";
import homePageData from "@/components/NewHomePage/components/data";
import HowOneAssureWork from "@/components/NewHomePage/components/HowOneAssureWork";
import AskAnythingSection from "@/components/NewHomePage/components/AskMeAnything/AskAnythingSection";
import HeroSection from "@/components/NewHomePage/components/HeroSection";
import HandBorder from "@/components/NewHomePage/components/HandBorder";
import TalkToHuman from "@/components/NewHomePage/components/TalkToHuman";
import MeetOurTeam from "@/components/NewHomePage/components/MeetOurTeam";
import TestimonialSection from "@/components/NewHomePage/components/TestimonialSection";
import { HomePageType } from "./types";

const index = ({data}: {data: HomePageType}) => {
  return (
    <div className="font-poppins">
      <HandBorder />
      <HeroSection {...data.HeroSection}/>
      {/* <WhyPeopleChoose
        pill_Content={data.whyToChooseUs.pill_Content}
        title={data.whyToChooseUs.title}
        cards={data.whyToChooseUs.cards}
      />
      <HowOneAssureWork
        pill_Content={data.howOneAssureWork.pill_Content}
        title={data.howOneAssureWork.title}
        cards={data.howOneAssureWork.cards}
      />
      <AskAnythingSection
        pill={homePageData.askAnythingSection.pill}
        heading={homePageData.askAnythingSection.heading}
        subheading={homePageData.askAnythingSection.subheading}
        slides={homePageData.askAnythingSection.slides}
      />

      <TestimonialSection 
        pill_Content={data.testimonialSection.pill_Content}
        title={data.testimonialSection.title}
        story={data.testimonialSection.story}
        testimonials={data.testimonialSection.testimonials}
      />

      <TalkToHuman />
      <MeetOurTeam {...data.meetTheTeam}/> */}
    </div>
  );
};

export default index;
