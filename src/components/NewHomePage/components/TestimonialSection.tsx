"use client";

import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainer from "@/components/globals/SectionContainer";
import {
  BodyLarge,
  BodyMedium,
  HeadingLarge,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import TestimonialCard from "./TestimonialCard";

export type TestimonialSectionProps = {
  pill_Content: string;
  title: string;
  story: {
    title: string;
    points: string[];
  };
  testimonials: {
    name: string;
    content?: string;
    video_url?: string;
  }[];
};

const TestimonialSection = ({
  pill_Content,
  title,
  story,
  testimonials,
}: TestimonialSectionProps) => {
  const storyPoints = story.points;

  const [currentIndex, setCurrentIndex] = useState(0);
  const [scrollProgress, setScrollProgress] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const testimonialRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % storyPoints.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [storyPoints.length]);

  // Get the style for each position in the list animation
  const getPositionStyle = (position: number) => {
    switch (position) {
      case 0:
        return "opacity-30 text-primary-600 bg-transparent scale-95";
      case 1:
        return "opacity-70 text-primary-700 bg-transparent scale-98";
      case 2:
        return "opacity-100 text-white bg-secondary-400 scale-100 shadow-md";
      case 3:
        return "opacity-70 text-primary-700 bg-transparent scale-98";
      case 4:
        return "opacity-30 text-primary-600 bg-transparent scale-95";
      default:
        return "opacity-0 scale-90";
    }
  };

  // Scroll-based animation for testimonial slider
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current || !testimonialRef.current) return;

      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      const scrollStart = -rect.top;
      const scrollEnd = rect.height - windowHeight;

      if (scrollStart > 0 && scrollStart < scrollEnd) {
        const progress = scrollStart / scrollEnd;
        setScrollProgress(progress);
      } else if (scrollStart <= 0) {
        setScrollProgress(0);
      } else if (scrollStart >= scrollEnd) {
        setScrollProgress(1);
      }
    };

    window.addEventListener("scroll", handleScroll);
    handleScroll();
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const maxScroll =
    testimonials.length * 400 + (testimonials.length - 1) * 32 - 800;
  const translateX = -(scrollProgress * maxScroll);
  const scrollHeight =
    typeof window !== "undefined" ? window.innerHeight * 3 : 3000;

  return (
    <div
      ref={containerRef}
      style={{ height: `${scrollHeight}px` }}
      className="mt-24"
    >
      <div className="sticky top-16 bg-primary-100 pt-16 pb-20 overflow-hidden">
        <SectionContainer>
          <Pill
            pill={pill_Content}
            border={false}
            textColor="text-secondary-400"
            bgColor="bg-white"
            className="mb-3"
          />
          <HeadingXLarge className="text-primary-800 text-center font-medium">
            {title}
          </HeadingXLarge>
        </SectionContainer>

        {/* ------------------ Main Content ------------------ */}
        <div className="flex flex-col md:flex-row gap-8 mt-16 mx-4 md:mx-0">
          {/* ----------- Story Section ----------- */}
          <div className="flex flex-col gap-4 flex-1 md:pl-28 md:pr-8 justify-center items-center md:items-start">
            <HeadingLarge className="text-primary-800 font-semibold inline-flex items-center gap-4">
              {story.title}
              <span>
                <Image
                  src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5b1b-025e-7670-869e-d2e797eb7ec7/Untitled_Artwork 40 2.png"
                  alt="brush stroke"
                  width={40}
                  height={40}
                />
              </span>
            </HeadingLarge>

            {/* Rotating Story Points */}
            <div className="relative h-[300px] flex items-center overflow-hidden w-full max-w-[400px] md:max-w-none">
              <div className="w-full relative" style={{ height: "300px" }}>
                {storyPoints.map((point, index) => {
                  const relativePosition =
                    (index - currentIndex + storyPoints.length) %
                    storyPoints.length;
                  const position = relativePosition < 5 ? relativePosition : -1;
                  const isVisible = position >= 0;
                  const topPosition = position * 70;

                  return (
                    <div
                      key={index}
                      className={`absolute left-0 right-0 transition-all duration-700 ease-in-out ${
                        isVisible ? "block" : "hidden"
                      }`}
                      style={{ top: `${topPosition}px` }}
                    >
                      <div
                        className={`px-6 py-4 rounded-full transition-all duration-700 ease-in-out ${getPositionStyle(
                          position
                        )}`}
                      >
                        {position === 2 ? (
                          <BodyLarge className="font-medium">{point}</BodyLarge>
                        ) : (
                          <BodyMedium>{point}</BodyMedium>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* ----------- Testimonials Section ----------- */}
          <div className="flex-1 flex overflow-hidden p-4 md:p-6 bg-white rounded-xl md:rounded-l-xl justify-center md:justify-start">
            {/* Desktop horizontal scroll animation */}
            <div
              ref={testimonialRef}
              className="hidden md:flex gap-8 transition-transform duration-100 ease-out"
              style={{ transform: `translateX(${translateX}px)` }}
            >
              {testimonials.map((testimonial, index) => (
                <TestimonialCard
                  key={index}
                  name={testimonial.name}
                  statement={testimonial.content}
                  video_url={testimonial.video_url}
                />
              ))}
            </div>

            {/* Mobile horizontal scroll (manual swipe) */}
            <div className="flex md:hidden gap-4 overflow-x-auto scroll-smooth snap-x snap-mandatory px-4 pb-4 w-full">
              {testimonials.map((testimonial, index) => (
                <div
                  ref={testimonialRef}
                  style={{ transform: `translateX(${translateX}px)` }}
                  key={index}
                  className="flex-shrink-0 snap-center w-[300px]"
                >
                  <TestimonialCard
                    name={testimonial.name}
                    statement={testimonial.content}
                    video_url={testimonial.video_url}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialSection;
