"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import { Button } from "@/components/UI/Button";
import {
  HeadingLarge,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import Image from "next/image";
import ChatModal from "../ChatModal";
import Pill from "@/components/globals/DSComponentsV0/Pill";
import RenderChatBubble from "./RenderChatBubble";

type SlideData = {
  title: string;
  subtitle: string;
  chats: ChatMessage[];
};

type ChatMessage = {
  id: string;
  type: "bot" | "human";
  content: string;
};

type AskAnythingSectionProps = {
  pill: string;
  heading: string;
  subheading: string;
  slides: SlideData[];
};

const AskAnythingSection: React.FC<AskAnythingSectionProps> = ({
  pill,
  heading,
  subheading,
  slides,
}) => {
  const [openModal, setOpenModal] = useState(false);
  const chatRefs = useRef<Record<string, HTMLDivElement | null>>({});

  const registerChatRef = useCallback(
    (key: string) => (element: HTMLDivElement | null) => {
      chatRefs.current[key] = element;
    },
    []
  );

  const handleOpenModal = () => setOpenModal(true);
  const handleCloseModal = () => setOpenModal(false);

  // ✅ Smooth auto-scroll down, instant reset to top
  const autoScrollLoop = (refKey: string) => {
    const scrollContainer = chatRefs.current[refKey];
    if (!scrollContainer) return;

    const scrollStep = 1; // scroll speed
    const interval = 20; // smaller = faster

    const scrollInterval = setInterval(() => {
      scrollContainer.scrollTop += scrollStep;

      if (
        scrollContainer.scrollTop + scrollContainer.clientHeight >=
        scrollContainer.scrollHeight
      ) {
        // Instantly jump to top
        scrollContainer.scrollTop = 0;
      }
    }, interval);

    return () => clearInterval(scrollInterval);
  };

  useEffect(() => {
    const cleanupDesktop = autoScrollLoop("desktop");
    const cleanupMobile = autoScrollLoop("mobile");
    return () => {
      cleanupDesktop?.();
      cleanupMobile?.();
    };
  }, []);

  if (!slides.length) return null;
  const activeSlide = slides[0];

  return (
    <SectionContainerLarge className="mt-12 md:mt-24 !p-0">
      <SectionContainerLarge>
        <Pill
          pill={pill}
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-secondary-100"
          className="mb-3"
        />
        <HeadingXLarge className="text-primary-800 text-center font-medium mb-5">
          {heading}
        </HeadingXLarge>
        <HeadingSmall className="text-primary-800 text-center font-normal mb-10">
          {subheading}
        </HeadingSmall>
      </SectionContainerLarge>

      {/* Desktop Layout */}
      <div className="relative hidden md:flex flex-col items-center w-full">
        <div className="bg-primary-200 overflow-hidden flex flex-col lg:flex-row rounded-4xl md:rounded-5xl mx-6 gap-8 px-5 md:px-0 w-full">
          {/* Left */}
          <div className="relative flex flex-col items-center text-center lg:flex-1">
            <HeadingLarge className="mt-20 text-primary-800 font-semibold mb-3">
              {activeSlide.title}
            </HeadingLarge>
            <HeadingLarge className="font-semibold text-primary-800 mb-6">
              {activeSlide.subtitle}
            </HeadingLarge>
            <Image
              src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5ac0-0273-7534-87c1-59b1dc0fd401/Untitled_Artwork 41 1.svg"
              alt="Badge"
              width={113}
              height={122}
              className="absolute top-36 right-9 rotate-6"
            />
            <Image
              src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5abf-bd03-7a35-b071-6344f9450661/Untitled_Artwork 40 2.svg"
              alt="Badge"
              width={113}
              height={122}
              className="absolute top-36 left-12 "
            />
            <div
              className="relative inline-block p-0.5 hover:cursor-pointer"
              onClick={handleOpenModal}
            >
              <div
                style={{ filter: "url(#squiggly)" }}
                className="absolute inset-0 border-4 border-primary-400 rounded-xl z-10"
              />
              <Button
                variant="primary"
                className="px-6 py-6 text-base font-normal rounded-xl hover:scale-100"
              >
                Ask me anything
              </Button>
            </div>

            <div className="overflow-hidden">
              <Image
                src={
                  "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5995-a5d8-799a-823b-4a6b534dfc65/girl.png"
                }
                alt="Customer support representative"
                width={435}
                height={500}
                className="hidden lg:block rounded-lg"
              />
            </div>
          </div>

          {/* Right */}
          <div className="relative overflow-hidden lg:flex-1 flex justify-end">
            <div className="relative w-full flex justify-center items-center">
              <div className="absolute top-16 right-13">
                <Image
                  src={
                    "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5dd5-497d-7ab4-ba25-9583ccbf78b0/Group 1000001647.svg"
                  }
                  alt="Mobile frame"
                  className="rounded-lg"
                  height={500}
                  width={435}
                />
                <div className="absolute top-16 left-6 right-6 h-[75%] pt-4">
                  <div>
                    <HeadingSmall className="text-primary-800 text-center">
                       OneAssure AI ChatBot
                    </HeadingSmall>
                  </div>
                  <div
                    ref={registerChatRef("desktop")}
                    className="relative h-full overflow-y-scroll no-scrollbar rounded-t-4xl bg-white p-3 flex flex-col gap-3"
                  >
                    {activeSlide.chats.map((chat) => (
                      <RenderChatBubble key={chat.id} chat={chat} />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Layout */}
      <div className="md:hidden p-2 ">
        <div className="relative bg-primary-300 h-[600px] flex flex-col rounded-4xl overflow-hidden gap-8 px-5 pt-8">
          <div className="flex flex-col items-center text-center">
            <HeadingLarge className="text-primary-800 font-normal mb-3">
              {activeSlide.title}
            </HeadingLarge>
            <HeadingLarge className="font-bold text-primary-800 mb-6">
              {activeSlide.subtitle}
            </HeadingLarge>
            <Image
              src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5ac0-0273-7534-87c1-59b1dc0fd401/Untitled_Artwork 41 1.svg"
              alt="Badge"
              width={80}
              height={80}
              className="absolute top-6 right-2  rotate-6"
            />
            <Image
              src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5abf-bd03-7a35-b071-6344f9450661/Untitled_Artwork 40 2.svg"
              alt="Badge"
              width={80}
              height={80}
              className="absolute top-24 left-4 "
            />
            <Button
              className="rounded-xl border-none px-8 py-4 text-lg mb-6"
              onClick={handleOpenModal}
            >
              Ask me anything
            </Button>
            <div className="relative">
              <Image
                src={
                  "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5dd5-497d-7ab4-ba25-9583ccbf78b0/Group 1000001647.svg"
                }
                alt="Mobile phone frame"
                className="rounded-lg"
                height={500}
                width={435}
              />
              <div className="absolute top-16 left-6 right-6 h-[75%] pt-1">
                <div>
                  <HeadingSmall className="text-primary-800 text-center">
                    One assure ai chat bot
                  </HeadingSmall>
                </div>
                <div
                  ref={registerChatRef("mobile")}
                  className="relative h-full overflow-y-scroll no-scrollbar rounded-t-4xl bg-white p-3 flex flex-col gap-3"
                >
                  {activeSlide.chats.map((chat) => (
                    <RenderChatBubble key={chat.id} chat={chat} />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {openModal && <ChatModal onClose={handleCloseModal} />}
    </SectionContainerLarge>
  );
};

export default AskAnythingSection;
