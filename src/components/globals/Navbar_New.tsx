"use client";

import useNavbarStore from "@/store/navbar";
import "animate.css";
import Image from "next/image";
import CustomLink from "./CustomLink";
import {
  useState,
  useEffect,
  Fragment,
  use,
  Dispatch,
  SetStateAction,
  useRef,
} from "react";
import ClaimsBtn from "./ClaimsBtn";
import { Popover } from "@headlessui/react";
import {
  MdOutlineHealthAndSafety,
  MdOutlineHealing,
  MdMenuBook,
} from "react-icons/md";
import { FaAngleDown } from "react-icons/fa6";

import Container from "./Container";
import MobileNavbar from "./MobileNavbar";

import { usePathname, useSearchParams } from "next/navigation";
import { XCircleIcon } from "@heroicons/react/24/outline";
import { Menu, Transition } from "@headlessui/react";
import {
  ArchiveBoxXMarkIcon,
  ChevronDownIcon,
  PencilIcon,
  Square2StackIcon,
  TrashIcon,
} from "@heroicons/react/16/solid";
import Link from "next/link";
import BookACallBtn from "./BookACall";
import BookACallSqrBtn from "./BookACallSqrBtn";
import { BodyMedium } from "../UI/Typography";
import SectionContainer from "./SectionContainer";
import { Button } from "../UI/Button";
import TalkToHumanBtn from "./TalkToHumanBtn";

function Navbar_New() {
  const pathname = usePathname();
  const search = useSearchParams();
  const utm = search.get("utm_source");
  const [toggle, setToggle] = useState(1);
  const [isOpen, setIsOpen] = useState(false);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const handleMenu = () => {
    setIsOpen(!isOpen);
  };

  // Prevent background scrolling when navbar is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }
    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  // Handle click outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        mobileMenuRef.current &&
        !mobileMenuRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  const business = [
    "/insurance/corporate-insurance",
    "/insurance/sell-insurance",
    "/one-cloud",
    "/business/home",
    "/business/group-health-life",
    "/business/liability-insurance",
  ];

  const navOpts = [
    [
      {
        title: "Health Insurance",
        url: "/health-insurance",
        subOpts: [
          {
            title: "Generate Quote",
            url: "/health-insurance",
          },
          {
            title: "Health Products",
            url: "/health-insurance/all-products",
          },
        ],
      },
      {
        title: "Term Insurance",
        url: "/term-insurance",
        subOpts: [
          {
            title: "Generate Quote",
            url: "/term-insurance",
          },
          {
            title: "Term Products",
            url: "/term-insurance/all-products",
          },
        ],
      },
      {
        title: "Blogs",
        url: "/insurance",
      },
      {
        title: "Claims",
        url: "/claims",
      },
    ],
    [
      {
        title: "Group Health & Life",
        url: "/business/group-health-life",
      },
      {
        title: "Liability Insurance",
        url: "/business/liability-insurance",
      },
    ],
  ];

  useEffect(() => {
    if (business.includes(pathname)) {
      setToggle(2);
    } else {
      setToggle(1);
    }
  }, [pathname]);

  const navbarStore = useNavbarStore();

  return (
    <>
        <SectionContainer
          className={`fixed inset-x-0 top-14 border rounded-xl border-primary-300 !p-3 z-50 backdrop-blur-md bg-white/30`}
        >
          <Container navSpacing={false}>
            <div className="flex justify-between items-center gap-8 md:gap-0">
              {/* Logo */}
              <div className="flex items-center">
                <CustomLink
                  href={`${toggle === 1 ? "/" : "/business/home"}`}
                  utm={utm}
                >
                  <div className="flex items-center cursor-pointer group">
                    <div className="w-[112.95px] h-8 md:w-[141.19px] md:h-10 relative transition-transform duration-300">
                      <Image
                        src="https://cdn.oasr.in/oa-site/cms-uploads/media/Logo_File_7th_August_4ea73a2feb.svg"
                        alt="Oneassure"
                        fill
                        style={{ objectFit: "contain" }}
                      />
                    </div>
                  </div>
                </CustomLink>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden md:flex items-center space-x-8">
                {navOpts[toggle - 1].map((opt, index) => (
                  <CustomLink key={index} href={opt.url} utm={utm}>
                    <BodyMedium
                      weight="medium"
                      className="text-neutral-1100 hover:text-primary-700 transition-colors duration-200"
                    >
                      {opt.title}
                    </BodyMedium>
                  </CustomLink>
                ))}
              </div>

              <div className="flex items-center gap-3 md:gap-0">
                {/* Book a Call Button */}
                <div className="block">
                  <TalkToHumanBtn
                    label="Talk to a Human"
                      className="rounded-xl gap-1 md:gap-0 px-4 py-5 md:py-0 md:px-5 h-8 md:h-10 text-sm/[1.125rem] md:text-[0.875rem]/[1.25rem]"
                  />
                </div>

                
                {/* Mobile Menu Button */}
                <div
                  onClick={handleMenu}
                  className="md:hidden hover:cursor-pointer py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  <div className="w-6 h-0.5 bg-gray-800 mb-1.5 rounded-full transition-all duration-300"></div>
                  <div className="w-6 h-0.5 bg-gray-800 my-1.5 rounded-full transition-all duration-300"></div>
                  <div className="w-6 h-0.5 bg-gray-800 mt-1.5 rounded-full transition-all duration-300"></div>
                </div>
              </div>
            </div>
          </Container>
        </SectionContainer>

      {/* Spacer to prevent content from being hidden behind fixed navbar */}
      <div className="h-[56px] md:h-[122px]" />

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Mobile Menu */}
      <div
        ref={mobileMenuRef}
        className={`fixed left-0 top-0 h-full w-[75%] bg-white/95 backdrop-blur-md flex flex-col transform -translate-x-full transition-all duration-300 ease-in-out z-50 pl-8 pr-4 shadow-2xl overflow-y-auto ${
          isOpen ? "transform translate-x-0" : ""
        }`}
      >
        <div
          className="mt-8 flex items-end justify-end mb-16"
          onClick={handleMenu}
        >
          <XCircleIcon className="text-gray-600 w-7 h-7 hover:text-gray-800 transition-colors duration-200" />
        </div>

        <ul className="space-y-6">
          {navOpts[toggle - 1].map((opt, idx) => (
            <CustomLink key={idx} href={opt.url} utm={utm}>
              <li
                onClick={() => setIsOpen(false)}
                className="border-b border-gray100 py-4 hover:translate-x-2 transition-transform duration-200"
              >
                <BodyMedium
                  weight="semibold"
                  className="text-neutral-1100 hover:text-primary-700 transition-colors duration-200"
                >
                  {opt.title}
                </BodyMedium>
              </li>
            </CustomLink>
          ))}
        </ul>

        {/* Mobile Buttons */}
        <div className="flex justify-center mt-12">
          <BookACallSqrBtn label="Talk to a Human" className="rounded-xl" />
        </div>
      </div>
    </>
  );
}

export default Navbar_New;