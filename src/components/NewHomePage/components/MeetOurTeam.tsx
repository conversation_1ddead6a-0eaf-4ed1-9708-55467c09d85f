import { BodyMedium, DisplayMedium } from "@/components/UI/Typography";
import React from "react";
import Image from "next/image";
import { htmlParser } from "@/utils/htmlParser";
import SectionContainer from "@/components/globals/SectionContainer";

type MeetOurTeamProps = {
  title?: string;
  image: string;
  description?: string;
};

const MeetOurTeam: React.FC<MeetOurTeamProps> = ({
  title = "Meet The Team",
  image,
  description = "",
}) => {
  return (
    <SectionContainer className="mt-12 md:mt-20 flex flex-col gap-8 overflow-hidden ">
      {/* Title */}
      <DisplayMedium className="text-primary-800 text-center font-semibold">
        {title}
      </DisplayMedium>

      {/* Description (with htmlParser) */}
      {description &&
        htmlParser(description, {
          components: {
            p: BodyMedium,
          },
          classNames: {
            p: "text-neutral-800 text-center ",
          },
        })}

      {/* Team Image */}
      <div className="flex-1">
        <Image
          className="rounded-lg w-full border-4 border-primary-400"
          width={990}
          height={520}
          src={image}
          alt="our-team"
        />
      </div>
    </SectionContainer>
  );
};

export default MeetOurTeam;
