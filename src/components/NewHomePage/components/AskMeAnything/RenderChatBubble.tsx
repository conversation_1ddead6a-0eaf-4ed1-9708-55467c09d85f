"use client";

import React from "react";
import Image from "next/image";

// Define the shape of a chat message
export type ChatMessage = {
  id: string;
  type: "bot" | "human";
  content: string;
};

type ChatBubbleProps = {
  chat: ChatMessage;
};

const BOT_ICON =
  "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5e17-7ec1-7118-9ff5-237ebf7d0768/mingcute_ai-fill.svg";
const HUMAN_ICON =
  "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5e17-9f5e-71b2-9284-6163eeed3b69/Gemini_Generated_Image_a7fj1ga7fj1ga7fj 1.svg";

const RenderChatBubble: React.FC<ChatBubbleProps> = ({ chat }) => {
  const isBot = chat.type === "bot";

  return (
    <div className={`flex ${isBot ? "justify-start" : "justify-end"}`}>
      <div className={`flex items-start gap-2 ${isBot ? "flex-row" : "flex-row-reverse"}`}>
        {/* Avatar */}
        <Image
          src={isBot ? BOT_ICON : HUMAN_ICON}
          alt={isBot ? "Bot avatar" : "User avatar"}
          width={24}
          height={24}
          className="mt-0.5 h-6 w-6"
        />

        {/* Message bubble */}
        <div
          className={`max-w-[75%] p-3 text-sm rounded-2xl ${
            isBot
              ? "bg-gray-100 text-gray-800 rounded-bl-none"
              : "bg-blue-500 text-white rounded-br-none"
          }`}
        >
          {chat.content}
        </div>
      </div>
    </div>
  );
};

export default RenderChatBubble;
