import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import SectionContainerXSmall from "@/components/globals/SectionContainerXSmall";
import { BodyMedium, Heading2XLarge } from "@/components/UI/Typography";
import React from "react";
import { Button } from "@/components/UI/Button";
import Image from "next/image";

const TalkToHuman = () => {
  return (
    <SectionContainerMedium className="!mt-24">
      <div className="bg-primary-100  border border-primary-300 rounded-xl relative ">
        <div className="flex flex-col relative z-10 py-12 px-8 md:px-16">
          <Heading2XLarge className="text-primary-800 font-medium mb-4">
            Still deciding? Talk to a human.
          </Heading2XLarge>
          <BodyMedium className="text-neutral-800 max-w-[168px] md:max-w-[550px] font-normal mb-6">
            Behind every policy, there's a bunch of humans who care about
            getting it right for you. Drop us a message - we'll be here.
          </BodyMedium>
          <div className="flex justify-center md:justify-start ">
            <div className="relative inline-block p-0.5 hover:cursor-pointer">
              <div
                style={{
                  filter: "url(#squiggly)",
                }}
                className="absolute inset-0 border-4 border-primary-400 rounded-xl z-10"
              />
              <Button
                variant="primary"
                className="px-6 py-6 text-base font-normal rounded-xl hover:scale-100"
              >
                Talk To A Human
              </Button>
            </div>
          </div>
        </div>
        <div className=" absolute bottom-0 right-0 hidden md:block translate-x-4 md:translate-x-8 lg:translate-x-12 lg:translate-y-5">
          <Image
            width={280}
            height={280}
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a9d-8534-7170-8565-754bf62895ce/boy.svg"
            alt="Boy illustration"
            className=" md:w-[300px] lg:w-[340px] xl:w-[390px] h-auto object-contain"
          />
        </div>
        <div className="absolute bottom-0 right-0 block md:hidden translate-x-4 md:translate-x-8 lg:translate-x-12 lg:translate-y-5">
            <Image
            width={190}
            height={165}
            src="https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a5a9d-8534-7170-8565-754bf62895ce/boy.svg"
            alt="Boy illustration"
            className="block md:hidden h-auto object-contain"
          />
        </div>
      </div>
    </SectionContainerMedium>
  );
};

export default TalkToHuman;
