import SectionContainerSmall from "@/components/globals/SectionContainerSmall";
import { Input } from "@/components/UI/Input";
import {
  Ellipsis,
  MessageCircleMore,
  Paperclip,
  Send,
  ThumbsUp,
  ThumbsDown,
  X,
} from "lucide-react";

type ChatModalProps = {
  onClose?: () => void;
};

const ChatModal = ({ onClose }: ChatModalProps) => {
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/80"
      onClick={onClose}
    >
      <div
        className="w-full max-w-5xl px-6 mb-12"
        onClick={(e) => e.stopPropagation()}
      >
        <SectionContainerSmall className="bg-white rounded-xl relative  overflow-hidden">
          {/* Top Section */}
          <div className="p-5 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Ellipsis className="text-gray-600" />
                <p className="text-gray-500">Chat with us!</p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-500 hover:text-gray-700"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Header */}
            <div className="flex items-center justify-between mt-5">
              <div className="flex items-center gap-3">
                <div className="p-2 border border-gray-200 rounded-full">
                  <MessageCircleMore className="text-blue-500" />
                </div>
                <div>
                  <h2 className="font-semibold text-lg">Chat with us!</h2>
                  <p className="text-gray-500 text-sm">OneAssure Chatbot</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <button className="text-gray-600 hover:text-gray-800">
                  <ThumbsUp size={24} />
                </button>
                <button className="text-gray-600 hover:text-gray-800">
                  <ThumbsDown size={24} />
                </button>
              </div>
            </div>
          </div>

          {/* Chat Body */}
          <div className="bg-gray-50 p-6 h-[290px] overflow-y-auto">
            <div className="flex flex-col items-end">
              <div className="bg-blue-600 text-white rounded-lg px-4 py-2 text-sm mb-1">
                Analyze Policy Document
              </div>
              <p className="text-gray-400 text-xs">Visitor 02:12 PM • Read</p>
            </div>
          </div>

          {/* Footer */}
          <div className=" p-4  bg-gray-50">
            <div className="relative">
              <Paperclip className="text-gray-400 absolute left-3 top-1/2 -translate-y-1/2" />
              <Input
                type="text"
                placeholder="Type a message..."
                className="w-full text-sm text-gray-700 rounded-md pl-10 pr-12 py-3 border border-gray-200 focus:ring-1 focus:ring-blue-400"
              />
              <button className="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-600 text-white p-2 rounded-full">
                <Send size={16} />
              </button>
            </div>
            <p className="text-center text-gray-400 text-xs pb-3 mt-2">
            Powered by <span className="text-blue-600 font-medium">OneAssure</span>
          </p>
          </div>
        </SectionContainerSmall>
      </div>
    </div>
  );
};

export default ChatModal;