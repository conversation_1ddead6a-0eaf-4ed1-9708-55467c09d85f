import Pill from "@/components/globals/DSComponentsV0/Pill";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { Button } from "@/components/UI/Button";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";
import {
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import { htmlParser } from "@/utils/htmlParser";
import React from "react";

type whyPeopleChooseCard = {
  icon: string;
  title: string;
  description: string;
};

type WhyPeopleChooseProps = {
  pill_Content: string;
  title: string;
  cards: whyPeopleChooseCard[];
};

const WhyPeopleChoose = ({
  pill_Content,
  title,
  cards,
}: WhyPeopleChooseProps) => {
  return (
    <SectionContainerLarge className="!mt-24 !px-0 ">
      <SectionContainerLarge className=" !px-0">
        <Pill
          pill={pill_Content}
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-secondary-100"
          className="mb-4"
        />
        <HeadingXLarge className="text-primary-800 text-center font-medium">
          {title}
        </HeadingXLarge>
      </SectionContainerLarge>

      <div className="mt-10">
        {/* Desktop view */}
        <div className="md:flex flex-col gap-4 hidden">
          {cards.map((card, index) => (
            <div
              key={index}
              className={`flex gap-20 justify-between ${
                index % 2 !== 0 ? "flex-row" : "flex-row-reverse"
              }`}
            >
              <div className="relative inline-block p-0.5 hover:cursor-pointer">
                <div className="w-[200px] h-[200px] flex items-center justify-center">
                  <img
                    src={
                      "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a6f15-c699-7cad-aaf5-e5c95cbb0c82/Gemini_Generated_Image_rjjychrjjychrjjy 1.svg"
                    }
                    alt={card.title}
                  />
                </div>
              </div>
              <div className="flex flex-col justify-center max-w-[560px]">
                <HeadingSmall className="font-bold text-primary-800 mb-2">
                  {card.title}
                </HeadingSmall>
                {htmlParser(card.description, {
                  classNames: {
                    p: "text-neutral-800 font-normal",
                  },
                })}
              </div>
            </div>
          ))}
        </div>
        {/* Mobile view */}
        <div className="block md:hidden">
          <MobileCarousel totalSlides={cards.length}>
            {cards.map((card, index) => (
              <MobileCarouselItem key={index} className="flex justify-center">
                <div className="flex flex-col text-center items-center gap-6 ">
                  {/* Icon */}
                  <div className="relative inline-block p-0.5">
                    <div className="w-[113px] h-[86px] flex items-center justify-center">
                      <img
                        src={
                          "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a6f15-c699-7cad-aaf5-e5c95cbb0c82/Gemini_Generated_Image_rjjychrjjychrjjy 1.svg"
                        }
                        alt={card.title}
                      />
                    </div>
                  </div>

                  {/* Text */}
                  <div className="w-full max-w-[305px">
                    <HeadingSmall className="font-bold text-primary-800 mb-2">
                      {card.title}
                    </HeadingSmall>
                    {htmlParser(card.description, {
                      classNames: {
                        p: "text-neutral-800 font-normal text-sm w-[300px] mx-auto",
                      },
                    })}
                  </div>
                </div>
              </MobileCarouselItem>
            ))}
          </MobileCarousel>
        </div>
      </div>
    </SectionContainerLarge>
  );
};

export default WhyPeopleChoose;
