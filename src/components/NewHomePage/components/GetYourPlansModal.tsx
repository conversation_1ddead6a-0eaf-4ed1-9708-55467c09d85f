import React from "react";
import SectionContainerMedium from "@/components/globals/SectionContainerMedium";
import { BodyMedium, BodySmall, HeadingLarge } from "@/components/UI/Typography";
import { X } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/UI/Select"; // Shadcn select import
import SectionContainerXSmall from "@/components/globals/SectionContainerXSmall";
import BookACallSqrBtn from "@/components/globals/BookACallSqrBtn";

// Types
type OptionType = {
  placeholder: string;
  items: string[];
};

type GetYourPlansProps = {
  title: string;
  subtitle: string;
  insuranceOptions: OptionType;
  insurerOptions: OptionType;
  policyOptions: OptionType;
  buttonText: string;
  onClose?: () => void;
};

const GetYourPlansModal = ({
  title,
  subtitle,
  insuranceOptions,
  insurerOptions,
  policyOptions,
  buttonText,
  onClose,
}: GetYourPlansProps) => {
  return (
    <div
      className="bg-black/80 min-h-screen flex justify-center items-center fixed inset-0 z-50"
      onClick={onClose}
    >
      <div
        className="w-full max-w-5xl mb-12 md:mb-14 px-6 md:px-0 mx-auto "
        onClick={(e) => e.stopPropagation()} // prevent modal close on inner click
      >
        <SectionContainerMedium className="bg-white rounded-md relative">
          <button
            onClick={onClose}
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700 text-xl"
          >
            <X className="h-5 w-5" />
          </button>

          <HeadingLarge className="text-center p-4">{title}</HeadingLarge>
          <BodySmall className="text-center mt-2 font-medium text-neutral-1100">{subtitle}</BodySmall>

          <SectionContainerXSmall className="flex flex-col md:flex-row justify-center items-center gap-4 mt-4 mb-3">
            {/* Insurance */}
            <Select >
              <SelectTrigger className="w-[250px] border !border-primary-800 rounded-xl px-6 py-3">
                <SelectValue className="text-primary-800" placeholder={insuranceOptions.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {insuranceOptions.items.map((item, i) => (
                  <SelectItem key={i} value={item}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {/* Insurer */}
            <Select >
              <SelectTrigger className="w-[250px] !border !border-primary-300 rounded-xl px-6 py-3">
                <SelectValue className="text-primary-800" placeholder={insurerOptions.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {insurerOptions.items.map((item, i) => (
                  <SelectItem key={i} value={item}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Policy */}
            <Select>
              <SelectTrigger className="w-[250px] !border !border-yellow-300 rounded-xl px-6 py-3">
                <SelectValue className="text-primary-800" placeholder={policyOptions.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {policyOptions.items.map((item, i) => (
                  <SelectItem key={i} value={item}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="block">
              <BookACallSqrBtn
                label="Get Your Plans Now"
                className="rounded-base md:rounded-xl gap-1 md:gap-0 px-2 md:px-5 h-8 md:h-10 text-sm/[1.125rem] md:text-[0.875rem]/[1.25rem]"
              />
            </div>
          </SectionContainerXSmall>
           hello
        </SectionContainerMedium>
      </div>
    </div>
  );
};

export default GetYourPlansModal;
