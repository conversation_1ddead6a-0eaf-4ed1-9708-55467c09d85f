// src/data/homePageData.ts

const homePageData = {
  whyToChooseUs: {
    pill_Content: "One Assure Benefits",
    title: "Why people choose OneAssure?",
    cards: [
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Smart, not spammy",
        description:
          "We help you choose confidently - not confuse you with 100 options.",
      },
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Real guidance, real care",
        description:
          "We listen, explain, and give you 100% unbiased recommendations - like a trusted friend who's done the homework. We care about the follow-through as much as the advice.",
      },
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Clear, no-jargon advice",
        description:
          "We break down the overwhelm into plain language - so you always know what's right for you.",
      },
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Support beyond sign-up",
        description:
          "From buying to claiming - we stick around. Even if you don't buy from us today, you'll leave with clarity and someone to come back to.",
      },
    ],
  },
  getYourPlanes :{
    title: "Know More About your Plans",
    subtitle: "Know More About your Plans",
    insuranceOptions: {
      placeholder: "Select Insurance",
      items: ["Health", "Life", "Car", "Travel"],
    },
    insurerOptions: {
      placeholder: "Select Insurer",
      items: ["ICICI Lombard", "HDFC Ergo", "Tata AIG"],
    },
    policyOptions: {
      placeholder: "Select Policy",
      items: ["Elevate Value", "Secure Plus", "Premium Care"],
    },
    buttonText: "Get your Plans Now",
  },
  howOneAssureWork: {
    pill: "OneAssure Benefits",
    heading: "How OneAssure Works?",
    steps: [
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Tell us what matters to you",
        description: "(Family cover? Low premiums? Easy claims?)",
      },
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "We pick the right options",
        description:
          "Plans that fit your needs - not a random list of everything.",
      },
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Choose with confidence",
        description:
          "Take your time. We'll keep you moving forward without the pressure.",
      },
      {
        icon: "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a590b-174a-78b0-a502-c399ef5864bd/hello.svg",
        title: "Get ongoing support",
        description:
          "Need to file a claim or renew? We're here - and we'll follow through.",
      },
    ],
  },

  askAnythingSection: {
    pill: "Help Center",
    heading: "Ask Me Anything",
    subheading: "From big life changes...",
    slides: [
      {
        title: "Confused About Claims?",
        subtitle: "We’re Here to Help",
        chats: [
          {
            id: "1",
            type: "human" as const,
            content: "Hello, how can I help you today?",
          },
          {
            id: "2",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "3",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "4",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "5",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "6",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "7",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "8",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "9",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "10",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "11",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "12",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "13",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "14",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
          {
            id: "15",
            type: "human" as const,
            content: "I need help with my claim.",
          },
          {
            id: "16",
            type: "bot" as const,
            content: "I'm having trouble with my claim.",
          },
        ],
      },
    ],
  },
};

export default homePageData;
