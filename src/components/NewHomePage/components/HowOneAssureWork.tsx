"use client";

import Image from "next/image";
import {
  BodyLarge,
  BodyMedium,
  HeadingSmall,
  HeadingXLarge,
} from "@/components/UI/Typography";
import SectionContainerLarge from "@/components/globals/SectionContainerLarge";
import Pill from "@/components/globals/DSComponentsV0/Pill";
import { htmlParser } from "@/utils/htmlParser";
import MobileCarousel from "@/components/UI/MobileCarousel";
import MobileCarouselItem from "@/components/UI/MobileCarouselItem";

type HowOneAssureWorkCard = {
  icon: string;
  title: string;
  description: string;
};

type HowOneAssureWork = {
  pill_Content: string;
  title: string;
  cards: HowOneAssureWorkCard[];
};

const HowOneAssureWork = ({ pill_Content, title, cards }: HowOneAssureWork) => {
  return (
    <SectionContainerLarge className="flex flex-col gap-10 !mt-24 !px-0">
      {/* Header Section */}
      <div className="flex flex-col items-center justify-center gap-3">
        <Pill
          pill={pill_Content}
          border={false}
          textColor="text-secondary-400"
          bgColor="bg-secondary-100"
        />
        <HeadingXLarge className="font-medium text-primary-800 text-center">
          {title}
        </HeadingXLarge>
      </div>

      {/* --- Desktop Grid (unchanged) --- */}
      <div className="hidden md:grid grid-cols-2 lg:grid-cols-4 gap-8">
        {cards.map((card, index) => (
          <div key={index} className="flex flex-col gap-4 items-center">
            <div className="relative  flex items-center">
              <Image
                src={
                  "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a6f15-c699-7cad-aaf5-e5c95cbb0c82/Gemini_Generated_Image_rjjychrjjychrjjy 1.svg"
                }
                alt={card.title}
                height={200}
                width={198}
                style={{ objectFit: "contain" }}
              />
            </div>

            <HeadingSmall className="font-bold text-neutral-1100 text-center">
              {card.title}
            </HeadingSmall>
            {htmlParser(card.description, {
              classNames: {
                p: "font-normal text-neutral-800 text-center",
              },
            })}
          </div>
        ))}
      </div>

      {/* --- Mobile Carousel (newly added) --- */}
      <div className="md:hidden flex justify-center items-center w-full">
        <MobileCarousel totalSlides={cards.length}>
          {cards.map((card, index) => (
            <MobileCarouselItem key={index}>
              <div className="flex flex-col items-center justify-center text-center gap-4 px-4">
                <div className="relative  flex items-center">
                  <Image
                    src={
                      "https://cdn.oasr.in/stream/oa-site/cms-uploads/media/019a6f15-c699-7cad-aaf5-e5c95cbb0c82/Gemini_Generated_Image_rjjychrjjychrjjy 1.svg"
                    }
                    alt={card.title}
                    height={140}
                    width={140}
                    style={{ objectFit: "contain" }}
                  />
                </div>

                <HeadingSmall className="font-bold text-neutral-1100">
                  {card.title}
                </HeadingSmall>

                {htmlParser(card.description, {
                  classNames: {
                    p: "font-normal text-neutral-800 text-sm text-center",
                  },
                })}
              </div>
            </MobileCarouselItem>
          ))}
        </MobileCarousel>
      </div>
    </SectionContainerLarge>
  );
};

export default HowOneAssureWork;
